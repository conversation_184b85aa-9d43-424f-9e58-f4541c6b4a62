// Get API base URL (same logic as api.js)
const getApiBaseUrl = () => {
  if (process.env.REACT_APP_API_URL) {
    return process.env.REACT_APP_API_URL;
  }

  if (process.env.NODE_ENV === 'production') {
    return '/api';
  }

  return 'http://localhost:5000/api';
};

// Cấu hình Gemini API cho client
const geminiConfig = {
  // Endpoints
  endpoints: {
    chat: `${getApiBaseUrl()}/chat/gemini`,
    test: `${getApiBaseUrl()}/chat/test-gemini`,
    analyze: `${getApiBaseUrl()}/analyze/gemini`
  },

  // C<PERSON>u hình mặc định cho chat UI
  ui: {
    maxMessageLength: 2000,
    typingSpeed: 30, // ms per character
    retryAttempts: 3,
    retryDelay: 1000, // ms
  },

  // <PERSON><PERSON><PERSON> thông báo lỗi
  errorMessages: {
    apiKeyMissing: 'Gemini API key chưa đư<PERSON>c cấu hình. Vui lòng liên hệ quản trị viên.',
    rateLimit: '<PERSON><PERSON> vượt quá giới hạn yêu cầu. Vui lòng thử lại sau.',
    invalidResponse: 'Không thể xử lý phản hồi từ Gemini. Vui lòng thử lại.',
    networkError: 'Lỗi kết nối. Vui lòng kiểm tra kết nối mạng của bạn.',
  },

  // Các prompt mặc định
  defaultPrompts: {
    greeting: 'Xin chào! Tôi có thể giúp gì cho bạn về bảo mật và an toàn thông tin?',
    error: 'Xin lỗi, đã có lỗi xảy ra. Vui lòng thử lại sau.',
    thinking: 'Đang xử lý yêu cầu của bạn...',
    retry: 'Đang thử lại...',
  }
};

export default geminiConfig; 