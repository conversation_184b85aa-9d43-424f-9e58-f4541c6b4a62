/* Tab-specific styles for enhanced UX */

/* Home Tab */
.tab-home {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-color: #667eea;
}

.tab-home .floating-particles {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Check Tab */
.tab-check {
  --primary-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  --accent-color: #11998e;
}

.tab-check .scanning-effect {
  animation: scan 3s linear infinite;
}

@keyframes scan {
  0% { transform: scale(0.8) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.2) rotate(180deg); opacity: 0.5; }
  100% { transform: scale(0.8) rotate(360deg); opacity: 1; }
}

/* Community Tab */
.tab-community {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-color: #8b5cf6;
}

.tab-community .social-pulse {
  animation: socialPulse 4s ease-in-out infinite;
}

@keyframes socialPulse {
  0%, 100% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.1); opacity: 1; }
}

/* Chat Tab */
.tab-chat {
  --primary-gradient: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
  --accent-color: #4f46e5;
}

.tab-chat .message-bubble {
  animation: messageBounce 0.3s ease-out;
}

@keyframes messageBounce {
  0% { transform: scale(0.8) translateY(10px); opacity: 0; }
  50% { transform: scale(1.05) translateY(-2px); opacity: 0.8; }
  100% { transform: scale(1) translateY(0); opacity: 1; }
}

/* Fix chat container scroll issues */
.tab-chat .chat-container {
  height: calc(100vh - 80px); /* Reduced height to account for header */
  max-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
}

.tab-chat .messages-area {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
  padding: 1rem;
  /* Ensure proper scrolling */
  min-height: 0;
}

.tab-chat .chat-input-area {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.dark .tab-chat .chat-input-area {
  background: rgba(31, 41, 55, 0.95);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Ensure scrollbar is visible and styled */
.tab-chat .messages-area::-webkit-scrollbar {
  width: 6px;
}

.tab-chat .messages-area::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.tab-chat .messages-area::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.tab-chat .messages-area::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Submit Tab */
.tab-submit {
  --primary-gradient: linear-gradient(135deg, #f97316 0%, #dc2626 100%);
  --accent-color: #f97316;
}

.tab-submit .creative-pulse {
  animation: creativePulse 3s ease-in-out infinite;
}

@keyframes creativePulse {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.7; }
  50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
}

/* My Submissions Tab */
.tab-my-submissions {
  --primary-gradient: linear-gradient(135deg, #14b8a6 0%, #06b6d4 100%);
  --accent-color: #14b8a6;
}

.tab-my-submissions .personal-content {
  animation: personalBounce 2.5s ease-in-out infinite;
}

@keyframes personalBounce {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-10px) scale(1.05); }
}

/* Dashboard Tab */
.tab-dashboard {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-color: #6366f1;
}

.tab-dashboard .data-viz {
  animation: dataFlow 2s ease-in-out infinite;
}

@keyframes dataFlow {
  0%, 100% { height: 20px; opacity: 0.6; }
  50% { height: 40px; opacity: 1; }
}

/* Messenger-like Chat Optimizations */
.messenger-layout {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messenger-header {
  flex-shrink: 0;
  height: 60px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.dark .messenger-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(31, 41, 55, 0.95);
}

.messenger-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.messenger-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  scroll-behavior: smooth;
  max-height: calc(100vh - 160px);
}

.messenger-input {
  flex-shrink: 0;
  padding: 12px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.dark .messenger-input {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(31, 41, 55, 0.95);
}

/* Message Bubbles */
.message-bubble {
  max-width: 70%;
  word-wrap: break-word;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.message-user {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 18px 18px 4px 18px;
  margin-left: auto;
}

.message-bot {
  background: rgba(243, 244, 246, 1);
  color: rgba(17, 24, 39, 1);
  border-radius: 18px 18px 18px 4px;
  margin-right: auto;
}

.dark .message-bot {
  background: rgba(55, 65, 81, 1);
  color: rgba(243, 244, 246, 1);
}

/* Scrollbar Styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.8);
}

/* Floating Action Button Positioning */
.floating-action-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 30;
  transition: all 0.3s ease;
}

.tab-chat .floating-action-button {
  bottom: 100px; /* Move up when in chat to avoid input area */
}

/* Chat Bot Positioning */
.chat-bot-button {
  position: fixed;
  bottom: 24px;
  left: 24px;
  z-index: 50;
}

.tab-chat .chat-bot-button {
  bottom: 100px; /* Move up when in chat */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .tab-chat .chat-container {
    height: calc(100vh - 80px);
  }
  
  .tab-chat .messages-area {
    max-height: calc(100vh - 140px);
  }
  
  .messenger-messages {
    max-height: calc(100vh - 120px);
    padding: 12px;
  }
  
  .floating-action-button {
    bottom: 16px;
    right: 16px;
  }
  
  .chat-bot-button {
    bottom: 16px;
    left: 16px;
  }
  
  .tab-chat .floating-action-button,
  .tab-chat .chat-bot-button {
    bottom: 80px;
  }
}

/* Legacy scroll fixes - Now handled by scroll-fix.css */
/* This section is kept for compatibility but overridden by scroll-fix.css */

/* Animation Performance */
.gpu-accelerated {
  transform-style: preserve-3d;
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: transform;
}

/* Enhanced Card Animations */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .card-hover:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Scroll triggered animations */
.scroll-trigger-left {
  transform: translateX(-100px) translateY(30px);
  opacity: 0;
}

.scroll-trigger-right {
  transform: translateX(100px) translateY(30px);
  opacity: 0;
}

/* Floating animation for cards */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating-card {
  animation: float 6s ease-in-out infinite;
}

.floating-card:nth-child(2) {
  animation-delay: -2s;
}

.floating-card:nth-child(3) {
  animation-delay: -4s;
}

/* Pulse animation for important elements */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    transform: scale(1.02);
  }
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Stagger animation delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }


