rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isValidUser() {
      return isAuthenticated() &&
             request.auth.token.email_verified == true;
    }

    function isVerifiedUser() {
      return isAuthenticated() &&
             request.auth.token.email_verified == true;
    }

    // Users collection - users can only access their own data
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow create: if isAuthenticated() &&
                       request.auth.uid == userId &&
                       request.resource.data.keys().hasAll(['email', 'firstName', 'lastName']) &&
                       request.resource.data.email == request.auth.token.email;
    }

    // Links collection - public read, authenticated write
    match /links/{linkId} {
      // Allow read for everyone (public community)
      allow read: if true;

      // Allow create for authenticated users
      allow create: if isAuthenticated() &&
                       request.resource.data.userId == request.auth.uid;

      // Allow update/delete only by owner
      allow update, delete: if isAuthenticated() &&
                               resource.data.userId == request.auth.uid;
    }

    // Verification tokens - only server can access
    match /verification_tokens/{tokenId} {
      allow read, write: if false; // Only server-side access via Admin SDK
    }

    // Password reset tokens - only server can access
    match /password_reset_tokens/{tokenId} {
      allow read, write: if false; // Only server-side access via Admin SDK
    }

    // Votes collection - users can vote and read vote statistics
    match /votes/{voteId} {
      allow read: if true; // Anyone can read vote statistics
      allow create: if isAuthenticated() &&
                       request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() &&
                               resource.data.userId == request.auth.uid;
    }

    // Comments collection - users can comment and read comments
    match /comments/{commentId} {
      allow read: if true; // Anyone can read comments
      allow create: if isAuthenticated() &&
                       request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAuthenticated() &&
                               resource.data.userId == request.auth.uid;
    }

    // Reports collection - users can submit reports, admins can manage
    match /reports/{reportId} {
      allow read: if isVerifiedUser() &&
                     (resource.data.userId == request.auth.uid ||
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      allow create: if isVerifiedUser() &&
                       request.resource.data.userId == request.auth.uid &&
                       request.resource.data.keys().hasAll(['linkId', 'userId', 'category', 'description']);
      allow update: if isVerifiedUser() &&
                       get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Admin notifications - only admins can access
    match /admin_notifications/{notificationId} {
      allow read, write: if isVerifiedUser() &&
                            get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Test collection - for testing Firestore connection
    match /test/{testId} {
      allow read, write: if isAuthenticated();
    }

    // Admin collection (if needed for future features)
    match /admin/{document=**} {
      allow read, write: if false; // Only server-side access
    }
  }
}
